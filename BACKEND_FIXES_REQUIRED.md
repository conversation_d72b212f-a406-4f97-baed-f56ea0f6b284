# Backend Database Schema Fixes Required

## Current Issues

### 1. Missing `assigned_at` Column in Orders Model

**Error**: `django.db.utils.ProgrammingError: column orders_order.assigned_at does not exist`

**Location**: Django backend - Orders model

**Fix Required**: Add the missing `assigned_at` field to the Order model and create a migration.

#### Django Model Fix:

```python
# In your orders/models.py
class Order(models.Model):
    # ... existing fields ...
    assigned_provider_id = models.ForeignKey(Provider, on_delete=models.SET_NULL, null=True, blank=True)
    assigned_at = models.DateTimeField(null=True, blank=True)  # ADD THIS FIELD
    # ... other fields ...
```

#### Migration Command:
```bash
python manage.py makemigrations orders
python manage.py migrate
```

#### Alternative Quick Fix (if you can't modify the model immediately):

Update your Django serializer to exclude the `assigned_at` field:

```python
# In your orders/serializers.py
class OrderSerializer(serializers.ModelSerializer):
    class Meta:
        model = Order
        fields = [
            'id', 'order_number', 'customer_name', 'customer_mobile',
            'status', 'total_amount', 'payment_method', 'provider_name',
            'created_at', 'items_count'
            # Remove 'assigned_at' from fields list temporarily
        ]
```

### 2. Dashboard API Endpoint Issues

**Error**: Similar database column issues affecting `/api/orders/dashboard/`

**Fix**: Ensure the dashboard endpoint only queries existing fields.

#### Dashboard View Fix:

```python
# In your orders/views.py or wherever dashboard stats are calculated
def get_dashboard_stats(request):
    try:
        stats = {
            'total_orders': Order.objects.count(),
            'pending_orders': Order.objects.filter(status='pending').count(),
            'confirmed_orders': Order.objects.filter(status='confirmed').count(),
            'in_progress_orders': Order.objects.filter(status='in_progress').count(),
            'completed_orders': Order.objects.filter(status='completed').count(),
            'cancelled_orders': Order.objects.filter(status='cancelled').count(),
            'recent_orders': Order.objects.select_related('customer').order_by('-created_at')[:5]
        }
        return Response(stats)
    except Exception as e:
        return Response({'error': str(e)}, status=500)
```

## Recommended Database Schema Updates

### 1. Orders Model Complete Schema:

```python
class Order(models.Model):
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('confirmed', 'Confirmed'),
        ('assigned', 'Assigned'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    
    order_number = models.CharField(max_length=50, unique=True)
    customer = models.ForeignKey(User, on_delete=models.CASCADE)
    customer_name = models.CharField(max_length=255)
    customer_mobile = models.CharField(max_length=20)
    customer_address = models.TextField()
    
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    payment_method = models.CharField(max_length=50, null=True, blank=True)
    payment_status = models.CharField(max_length=20, default='pending')
    
    assigned_provider = models.ForeignKey('Provider', on_delete=models.SET_NULL, null=True, blank=True)
    assigned_at = models.DateTimeField(null=True, blank=True)  # REQUIRED FIELD
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    @property
    def provider_name(self):
        return self.assigned_provider.name if self.assigned_provider else None
    
    @property
    def items_count(self):
        return self.orderitem_set.count()
```

### 2. Migration Script:

```python
# Create this migration file manually if needed
from django.db import migrations, models

class Migration(migrations.Migration):
    dependencies = [
        ('orders', '0001_initial'),  # Replace with your latest migration
    ]

    operations = [
        migrations.AddField(
            model_name='order',
            name='assigned_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
    ]
```

## Frontend Error Handling (Already Implemented)

The Next.js frontend now includes:

1. **Error Logging**: Console logging of API errors for debugging
2. **Graceful Degradation**: Pages will show error states instead of crashing
3. **User-Friendly Messages**: Clear error messages for users
4. **Retry Mechanisms**: Users can retry failed operations

## Testing After Backend Fixes

Once you've applied the backend fixes:

1. **Test Orders API**: `GET /api/orders/`
2. **Test Dashboard API**: `GET /api/orders/dashboard/`
3. **Test Order Creation**: `POST /api/orders/`
4. **Test Provider Assignment**: `POST /api/orders/{id}/assign-provider/`

## Quick Temporary Workaround

If you need the frontend to work immediately while fixing the backend:

1. **Mock Data**: The frontend can fall back to mock data when API calls fail
2. **Disable Features**: Temporarily disable provider assignment features
3. **Error Boundaries**: Show user-friendly error messages

## Priority Order for Fixes:

1. **High Priority**: Add `assigned_at` field to Order model
2. **High Priority**: Fix dashboard API endpoint
3. **Medium Priority**: Ensure all API endpoints return expected fields
4. **Low Priority**: Add comprehensive error handling in Django views

## Commands to Run:

```bash
# 1. Add the field to your model
# 2. Create migration
python manage.py makemigrations orders

# 3. Apply migration
python manage.py migrate

# 4. Restart Django server
python manage.py runserver

# 5. Test the endpoints
curl http://localhost:8000/api/orders/
curl http://localhost:8000/api/orders/dashboard/
```

The frontend is now robust enough to handle these backend issues gracefully while you implement the fixes.
