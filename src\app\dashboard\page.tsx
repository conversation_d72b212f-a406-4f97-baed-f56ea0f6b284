'use client';

import React from 'react';
import {
  ShoppingBag,
  Users,
  UserCheck,
  DollarSign,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Card, CardHeader, CardTitle, CardContent, StatsCard } from '@/components/ui/Card';
import { LoadingCard } from '@/components/ui/LoadingSpinner';
import { Badge } from '@/components/ui/Badge';
import { useDashboardStats } from '@/hooks/useAnalytics';
import { formatCurrency, formatRelativeTime } from '@/lib/utils';
import { Order } from '@/types/api';

export default function DashboardPage() {
  const { data: stats, isLoading: loading, error } = useDashboardStats();

  // Log error for debugging
  if (error && !loading) {
    console.error('Dashboard API Error:', error);
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          
          {/* Stats Cards Loading */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {Array.from({ length: 4 }).map((_, i) => (
              <LoadingCard key={i} />
            ))}
          </div>
          
          {/* Recent Orders Loading */}
          <LoadingCard />
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <AlertCircle className="mx-auto h-12 w-12 text-red-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Error loading dashboard</h3>
          <p className="mt-1 text-sm text-gray-500">{(error as any)?.message || 'Failed to load dashboard data'}</p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-sm text-gray-500">
            Last updated: {new Date().toLocaleTimeString()}
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatsCard
            title="Total Orders"
            value={stats?.total_orders || 0}
            icon={<ShoppingBag className="w-6 h-6" />}
            change={{
              value: `${stats?.pending_orders || 0} pending`,
              type: 'increase'
            }}
          />
          
          <StatsCard
            title="Confirmed Orders"
            value={stats?.confirmed_orders || 0}
            icon={<CheckCircle className="w-6 h-6" />}
          />

          <StatsCard
            title="In Progress"
            value={stats?.in_progress_orders || 0}
            icon={<Clock className="w-6 h-6" />}
          />

          <StatsCard
            title="Completed Orders"
            value={stats?.completed_orders || 0}
            icon={<TrendingUp className="w-6 h-6" />}
          />
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Clock className="w-5 h-5 mr-2 text-yellow-500" />
                Pending Orders
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">
                {stats?.pending_orders || 0}
              </div>
              <p className="text-sm text-gray-500">Awaiting processing</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CheckCircle className="w-5 h-5 mr-2 text-green-500" />
                Cancelled Orders
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {stats?.cancelled_orders || 0}
              </div>
              <p className="text-sm text-gray-500">Cancelled orders</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="w-5 h-5 mr-2 text-blue-500" />
                Order Status Overview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Total:</span>
                  <span className="font-medium">{stats?.total_orders || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">In Progress:</span>
                  <span className="font-medium">{stats?.in_progress_orders || 0}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Orders */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Orders</CardTitle>
          </CardHeader>
          <CardContent>
            {!stats?.recent_orders || stats.recent_orders.length === 0 ? (
              <p className="text-gray-500 text-center py-4">No recent orders</p>
            ) : (
              <div className="space-y-4">
                {stats.recent_orders.map((order) => (
                  <div key={order.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <span className="font-medium text-gray-900">#{order.order_number}</span>
                        <Badge variant="info" size="sm">{order.status}</Badge>
                      </div>
                      <div className="mt-1 text-sm text-gray-500">
                        {order.customer_name} • {order.items_count} item{order.items_count !== 1 ? 's' : ''}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium text-gray-900">
                        {formatCurrency(order.total_amount)}
                      </div>
                      <div className="text-sm text-gray-500">
                        {formatRelativeTime(order.created_at)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
